{"name": "esus-audit-ai", "version": "1.0.0", "description": "AI-powered audit automation platform for finance and audit firms", "main": "index.js", "private": true, "workspaces": ["client", "server", "shared"], "scripts": {"start": "concurrently \"npm run start:client\" \"npm run start:server\"", "start:client": "cd client && npm start", "start:server": "cd server && func start", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "test": "npm run test:client && npm run test:server", "test:client": "cd client && npm test", "test:server": "cd server && npm test", "test:coverage": "npm run test:client -- --coverage && npm run test:server -- --coverage", "lint": "npm run lint:client && npm run lint:server", "lint:client": "cd client && npm run lint", "lint:server": "cd server && npm run lint", "lint:fix": "npm run lint:client -- --fix && npm run lint:server -- --fix", "deploy": "./scripts/deploy.sh", "init": "./scripts/init-local.sh", "db:setup": "psql -f database/schema.sql && psql -f database/seed.sql", "clean": "npm run clean:client && npm run clean:server", "clean:client": "cd client && rm -rf node_modules dist", "clean:server": "cd server && rm -rf node_modules dist", "install:all": "npm install && npm run install:client && npm run install:server", "install:client": "cd client && npm install", "install:server": "cd server && npm install"}, "keywords": ["audit", "ai", "finance", "azure", "react", "nodejs"], "author": "Esus Audit AI Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "axios": "^1.9.0", "jspdf": "^2.5.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}