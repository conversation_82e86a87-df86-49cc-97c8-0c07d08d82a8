#!/usr/bin/env node

/**
 * Registration Endpoint Testing Script
 * Tests the /api/register endpoint to debug 500 errors
 */

const axios = require('axios');
const { Pool } = require('pg');

// Configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:7071/api';
const DATABASE_URL = process.env.DATABASE_URL || '************************************************************************************/esusauditai?sslmode=require';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorLog(color, prefix, message) {
  console.log(`${colors[color]}[${prefix}]${colors.reset} ${message}`);
}

function info(message) { colorLog('blue', 'INFO', message); }
function success(message) { colorLog('green', 'SUCCESS', message); }
function warning(message) { colorLog('yellow', 'WARNING', message); }
function error(message) { colorLog('red', 'ERROR', message); }

// Test data
const testUser = {
  email: `test.user.${Date.now()}@example.com`,
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User',
  company: 'Test Company',
  role: 'auditor'
};

async function testDatabaseConnection() {
  info('Testing database connection...');
  
  try {
    const pool = new Pool({
      connectionString: DATABASE_URL,
      ssl: { rejectUnauthorized: false }
    });
    
    const client = await pool.connect();
    const result = await client.query('SELECT version()');
    client.release();
    await pool.end();
    
    success(`Database connected: ${result.rows[0].version.substring(0, 50)}...`);
    return true;
  } catch (err) {
    error(`Database connection failed: ${err.message}`);
    return false;
  }
}

async function testServerHealth() {
  info('Testing server health...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/health`, {
      timeout: 10000
    });
    
    success(`Server is healthy: ${response.status}`);
    console.log('Health response:', response.data);
    return true;
  } catch (err) {
    if (err.code === 'ECONNREFUSED') {
      error('Server is not running or not accessible');
    } else {
      error(`Health check failed: ${err.message}`);
    }
    return false;
  }
}

async function testRegistrationEndpoint() {
  info('Testing registration endpoint...');
  
  try {
    console.log('Sending registration request with data:', {
      ...testUser,
      password: '[HIDDEN]'
    });
    
    const response = await axios.post(`${API_BASE_URL}/register`, testUser, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    success(`Registration successful: ${response.status}`);
    console.log('Registration response:', {
      ...response.data,
      token: response.data.token ? '[TOKEN_PRESENT]' : '[NO_TOKEN]'
    });
    return true;
    
  } catch (err) {
    error(`Registration failed: ${err.message}`);
    
    if (err.response) {
      error(`Status: ${err.response.status}`);
      error(`Response: ${JSON.stringify(err.response.data, null, 2)}`);
      
      // Detailed error analysis
      if (err.response.status === 500) {
        warning('500 Error Analysis:');
        console.log('- Check server logs for detailed error information');
        console.log('- Verify all environment variables are set');
        console.log('- Ensure database is accessible and schema is correct');
        console.log('- Check JWT_SECRET is configured');
      }
    } else if (err.code === 'ECONNREFUSED') {
      error('Connection refused - server is not running');
    } else {
      error(`Network error: ${err.code || 'Unknown'}`);
    }
    
    return false;
  }
}

async function testEnvironmentVariables() {
  info('Checking environment variables...');
  
  const requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'AZURE_OPENAI_ENDPOINT',
    'AZURE_OPENAI_API_KEY'
  ];
  
  let allPresent = true;
  
  for (const varName of requiredVars) {
    if (process.env[varName]) {
      success(`${varName}: Present`);
    } else {
      error(`${varName}: Missing`);
      allPresent = false;
    }
  }
  
  return allPresent;
}

async function testUserTableSchema() {
  info('Testing users table schema...');
  
  try {
    const pool = new Pool({
      connectionString: DATABASE_URL,
      ssl: { rejectUnauthorized: false }
    });
    
    const client = await pool.connect();
    
    // Check if users table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `);
    
    if (!tableCheck.rows[0].exists) {
      error('Users table does not exist');
      client.release();
      await pool.end();
      return false;
    }
    
    // Check table structure
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position;
    `);
    
    success('Users table exists with columns:');
    columns.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    client.release();
    await pool.end();
    return true;
    
  } catch (err) {
    error(`Schema check failed: ${err.message}`);
    return false;
  }
}

async function cleanupTestUser() {
  info('Cleaning up test user...');
  
  try {
    const pool = new Pool({
      connectionString: DATABASE_URL,
      ssl: { rejectUnauthorized: false }
    });
    
    const client = await pool.connect();
    await client.query('DELETE FROM users WHERE email = $1', [testUser.email]);
    client.release();
    await pool.end();
    
    success('Test user cleaned up');
  } catch (err) {
    warning(`Cleanup failed: ${err.message}`);
  }
}

async function main() {
  console.log(`${colors.cyan}🧪 Esus Audit AI Registration Testing Script${colors.reset}\n`);
  
  const tests = [
    { name: 'Environment Variables', fn: testEnvironmentVariables },
    { name: 'Database Connection', fn: testDatabaseConnection },
    { name: 'Users Table Schema', fn: testUserTableSchema },
    { name: 'Server Health', fn: testServerHealth },
    { name: 'Registration Endpoint', fn: testRegistrationEndpoint }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    console.log(`\n${'='.repeat(50)}`);
    console.log(`Testing: ${test.name}`);
    console.log(`${'='.repeat(50)}`);
    
    try {
      const result = await test.fn();
      if (result) {
        passed++;
        success(`✅ ${test.name} PASSED`);
      } else {
        failed++;
        error(`❌ ${test.name} FAILED`);
      }
    } catch (err) {
      failed++;
      error(`❌ ${test.name} ERROR: ${err.message}`);
    }
  }
  
  // Cleanup
  await cleanupTestUser();
  
  // Summary
  console.log(`\n${'='.repeat(50)}`);
  console.log(`TEST SUMMARY`);
  console.log(`${'='.repeat(50)}`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);
  
  if (failed === 0) {
    success('🎉 All tests passed! Registration should work.');
  } else {
    error('🚨 Some tests failed. Check the errors above.');
    
    console.log('\n💡 Troubleshooting Tips:');
    console.log('1. Ensure Azure Functions Core Tools is installed');
    console.log('2. Start the server with: func start');
    console.log('3. Check local.settings.json has all required variables');
    console.log('4. Verify database connection and schema');
    console.log('5. Check server logs for detailed error messages');
  }
  
  process.exit(failed > 0 ? 1 : 0);
}

if (require.main === module) {
  main().catch(err => {
    error(`Script failed: ${err.message}`);
    process.exit(1);
  });
}

module.exports = { main };
