{"name": "esus-audit-ai-client", "version": "1.0.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-dropzone": "^14.2.3", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "html2canvas": "^1.4.1", "date-fns": "^2.30.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "@vitest/coverage-v8": "^1.0.4"}, "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "start": "vite --host", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "echo 'Linting client code...'", "lint:fix": "echo 'Fixing client code...'"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}