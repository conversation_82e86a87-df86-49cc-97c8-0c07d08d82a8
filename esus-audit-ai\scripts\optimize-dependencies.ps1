# Dependency Optimization Script for Esus Audit AI (PowerShell)
# This script cleans up duplicate dependencies and reinstalls with workspace optimization

param(
    [switch]$Force = $false
)

# Function to write colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

Write-Host "🧹 Starting dependency optimization..." -ForegroundColor Cyan

# Check if we're in the right directory
if (-not (Test-Path "package.json") -or -not (Test-Path "client") -or -not (Test-Path "server")) {
    Write-Error "Please run this script from the esus-audit-ai root directory"
    exit 1
}

Write-Status "Cleaning existing node_modules..."

# Remove all node_modules directories
$foldersToRemove = @(
    "node_modules",
    "client\node_modules",
    "server\node_modules",
    "shared\node_modules"
)

foreach ($folder in $foldersToRemove) {
    if (Test-Path $folder) {
        Write-Status "Removing $folder..."
        Remove-Item $folder -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Remove package-lock files
$filesToRemove = @(
    "package-lock.json",
    "client\package-lock.json",
    "server\package-lock.json",
    "shared\package-lock.json"
)

foreach ($file in $filesToRemove) {
    if (Test-Path $file) {
        Write-Status "Removing $file..."
        Remove-Item $file -Force -ErrorAction SilentlyContinue
    }
}

Write-Success "Cleaned existing dependencies"

Write-Status "Installing optimized dependencies..."

# Install dependencies with workspace support
try {
    npm install
    Write-Success "Dependencies installed with workspace optimization"
} catch {
    Write-Error "Failed to install dependencies: $_"
    exit 1
}

Write-Status "Verifying workspace setup..."

# Check if workspaces are properly configured
try {
    $null = npm ls --workspaces 2>$null
    Write-Success "Workspace configuration verified"
} catch {
    Write-Warning "Workspace configuration may have issues"
}

Write-Status "Running dependency audit..."

# Check for vulnerabilities
try {
    npm audit --audit-level=moderate
} catch {
    Write-Warning "Some vulnerabilities found - consider running 'npm audit fix'"
}

Write-Status "Checking for duplicate dependencies..."

# List duplicate dependencies
try {
    $duplicates = npm ls --all 2>$null | Select-String -Pattern "deduped|UNMET"
    if ($duplicates) {
        Write-Warning "Some duplicates found:"
        $duplicates | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
    } else {
        Write-Success "No obvious duplicates found"
    }
} catch {
    Write-Success "No obvious duplicates found"
}

Write-Success "Dependency optimization complete!"

Write-Host ""
Write-Host "📊 Summary:" -ForegroundColor Cyan
Write-Host "- Converted to npm workspaces"
Write-Host "- Moved shared dependencies to root"
Write-Host "- Created shared utilities workspace"
Write-Host "- Eliminated duplicate axios and jspdf"
Write-Host "- Optimized testing library usage"

Write-Host ""
Write-Host "🚀 Next steps:" -ForegroundColor Cyan
Write-Host "1. Run 'npm test' to verify all tests pass"
Write-Host "2. Run 'npm run build' to verify builds work"
Write-Host "3. Check the docs/DEPENDENCY_OPTIMIZATION.md for details"

Write-Host ""
Write-Success "All done! Your dependencies are now optimized."
