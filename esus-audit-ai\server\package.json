{"name": "esus-audit-ai-server", "version": "1.0.0", "description": "Azure Functions backend for Esus Audit AI", "main": "src/app.js", "scripts": {"start": "func start", "dev": "func start", "dev:watch": "func start --verbose", "build": "npm run test && npm run lint", "lint": "eslint src/ shared/ --ext .js", "lint:fix": "eslint src/ shared/ --ext .js --fix", "validate": "node scripts/validate-setup.js", "setup": "npm install && npm run validate", "db:setup": "node database/setup.js", "db:test": "node database/setup.js --test", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "deploy:dev": "func azure functionapp publish esus-audit-ai-functions-dev", "deploy:staging": "func azure functionapp publish esus-audit-ai-functions-staging", "deploy:prod": "func azure functionapp publish esus-audit-ai-functions-prod", "deploy": "npm run deploy:dev"}, "dependencies": {"@azure/ai-form-recognizer": "^5.0.0", "@azure/functions": "^4.0.0", "@azure/search-documents": "^12.0.0", "@azure/service-bus": "^7.9.4", "@azure/storage-blob": "^12.17.0", "applicationinsights": "^2.9.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "html-pdf": "^3.0.1", "jsonwebtoken": "^9.0.2", "mammoth": "^1.6.0", "multer": "^1.4.5-lts.1", "openai": "^4.20.1", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/jest": "^29.5.5", "eslint": "^8.50.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-n": "^16.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "mongodb-memory-server": "^8.15.1", "nock": "^13.3.3", "supertest": "^6.3.3"}}