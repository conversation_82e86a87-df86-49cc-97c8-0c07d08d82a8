This document contains the list of Third Party Software included with
PhantomJS, along with the license information.

Third Party Software may impose additional restrictions and it is the
user's responsibility to ensure that they have met the licensing
requirements of PhantomJS and the relevant license of the Third Party
Software they are using.

Qt - http://qt-project.org/
License: GNU Lesser General Public License (LGPL) version 2.1.
Reference: http://qt-project.org/doc/qt-4.8/lgpl.html.

WebKit - http://www.webkit.org/
License: GNU Lesser General Public License (LGPL) version 2.1 and BSD.
Reference: http://www.webkit.org/coding/lgpl-license.html and
http://www.webkit.org/coding/bsd-license.html.

Mongoose - https://github.com/cesanta/mongoose
License: MIT
Reference: https://github.com/cesanta/mongoose/commit/abbf27338ef554cce0281ac157aa71a9c1b82a55

OpenSSL - http://www.openssl.org/
License: OpenSSL License, SSLeay License.
Reference: http://www.openssl.org/source/license.html.

Linenoise - https://github.com/tadmarshall/linenoise
License: BSD.
Reference: https://github.com/tadmarshall/linenoise/blob/master/linenoise.h.

QCommandLine - http://xf.iksaif.net/dev/qcommandline.html
License: GNU Lesser General Public License (LGPL) version 2.1.
Reference: http://dev.iksaif.net/projects/qcommandline/repository/revisions/master/entry/COPYING

wkhtmlpdf - http://code.google.com/p/wkhtmltopdf/
License: GNU Lesser General Public License (LGPL)
Reference: http://code.google.com/p/wkhtmltopdf/
