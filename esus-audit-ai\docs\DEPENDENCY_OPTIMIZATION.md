# Dependency Optimization Guide

## Overview
This document outlines the optimization of duplicate dependencies and testing libraries in the Esus Audit AI project.

## Issues Identified

### 1. Duplicate Test Libraries
- **@bcoe/v8-coverage**: Automatically installed by both <PERSON><PERSON><PERSON> (client) and <PERSON><PERSON> (server)
- **TypeScript definitions**: Identical across both directories
- **Coverage tools**: Separate coverage setups causing redundancy

### 2. Duplicate Dependencies
- **jspdf**: `^2.5.1` in both client and server
- **axios**: Different versions (`^1.6.2` client vs `^1.9.0` server)

### 3. Redundant Environment Setup
- Multiple scripts creating similar environment files
- Duplicate configuration patterns

## Optimization Strategy

### 1. Workspace Configuration
- Converted to npm workspaces for better dependency management
- Created shared workspace for common dependencies
- Centralized version management

### 2. Dependency Consolidation
- Moved shared dependencies to root `package.json`
- Created `shared/` workspace for common utilities
- Standardized axios and jspdf versions

### 3. Testing Strategy
- **Client**: Vitest for React components (optimal for Vite)
- **Server**: Jest for Node.js/Azure Functions (optimal for backend)
- **Coverage**: Separate but coordinated coverage reporting

## File Structure Changes

```
esus-audit-ai/
├── package.json (root workspace)
├── shared/
│   ├── package.json
│   └── index.js (shared utilities)
├── client/
│   └── package.json (React-specific deps)
└── server/
    └── package.json (Azure Functions deps)
```

## Benefits

### 1. Reduced Duplication
- Single source of truth for shared dependencies
- Consistent versions across workspaces
- Smaller node_modules footprint

### 2. Better Maintainability
- Centralized dependency updates
- Clear separation of concerns
- Easier version management

### 3. Improved Performance
- Faster installs with workspace hoisting
- Reduced disk usage
- Better caching

## Usage

### Installation
```bash
# Install all dependencies
npm run install:all

# Install specific workspace
npm run install:client
npm run install:server
```

### Testing
```bash
# Run all tests
npm test

# Run specific tests
npm run test:client
npm run test:server

# Coverage reports
npm run test:coverage
```

### Building
```bash
# Build all
npm run build

# Build specific
npm run build:client
npm run build:server
```

### Linting
```bash
# Lint all
npm run lint

# Fix all
npm run lint:fix
```

## Shared Dependencies

### Current Shared Dependencies
- **axios**: `^1.9.0` - HTTP client
- **jspdf**: `^2.5.1` - PDF generation

### Usage in Code
```javascript
// Import from shared module
const { axios, jsPDF } = require('@esus-audit-ai/shared');

// Or import directly (workspace will resolve to shared version)
const axios = require('axios');
const jsPDF = require('jspdf');
```

## Testing Libraries Strategy

### Client (Vitest)
- **Why**: Optimized for Vite/React
- **Coverage**: @vitest/coverage-v8
- **Benefits**: Fast, modern, ESM support

### Server (Jest)
- **Why**: Mature, Azure Functions compatible
- **Coverage**: Built-in Jest coverage
- **Benefits**: Extensive mocking, Node.js optimized

### Coverage Consolidation
Both testing frameworks use v8 coverage under the hood, ensuring consistent coverage metrics.

## Migration Notes

### Breaking Changes
- Dependencies moved to workspace root
- Import paths may need updates for shared utilities
- Test commands now run through workspace scripts

### Compatibility
- All existing functionality preserved
- Azure Functions deployment unchanged
- React development workflow unchanged

## Future Optimizations

1. **ESLint Configuration**: Shared ESLint config
2. **TypeScript**: Shared tsconfig.json
3. **Build Tools**: Shared build utilities
4. **Testing Utilities**: Shared test helpers
