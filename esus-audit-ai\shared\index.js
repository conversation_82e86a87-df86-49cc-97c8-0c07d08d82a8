// Shared utilities and re-exports for Esus Audit AI
// This module provides common dependencies and utilities used across client and server

// Re-export axios with consistent configuration
const axios = require('axios');

// Configure axios defaults
const apiClient = axios.create({
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Re-export jsPDF
const jsPDF = require('jspdf');

module.exports = {
  axios: apiClient,
  jsPDF,
  // Add other shared utilities here
};
