{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "node", "NODE_ENV": "development", "DATABASE_URL": "************************************************************************************/esusauditai?sslmode=require", "AZURE_STORAGE_CONNECTION_STRING": "DefaultEndpointsProtocol=https;AccountName=esusauditai;AccountKey=your_storage_key;EndpointSuffix=core.windows.net", "AZURE_STORAGE_CONTAINER_NAME": "documents", "AZURE_FORM_RECOGNIZER_ENDPOINT": "https://esusauditai.cognitiveservices.azure.com/", "AZURE_FORM_RECOGNIZER_KEY": "your_form_recognizer_key_here", "AZURE_OPENAI_ENDPOINT": "https://esusaudiau.openai.azure.com/", "AZURE_OPENAI_API_KEY": "your_openai_key_here", "AZURE_OPENAI_DEPLOYMENT_NAME": "gpt-4o-mini", "AZURE_OPENAI_API_VERSION": "2024-02-15-preview", "AZURE_SEARCH_ENDPOINT": "https://esusauditai.search.windows.net", "AZURE_SEARCH_API_KEY": "your_search_key_here", "AZURE_SEARCH_INDEX_NAME": "esus-audit-documents", "JWT_SECRET": "399a5c5f84214e2623178a18f210b3c0dadc8c78d17686be64a0512ed0438808c09f9be23e63d0c3c0cf98f7627749b5b00dcc57fe27d94e0290132bcc689700", "JWT_EXPIRES_IN": "24h"}, "Host": {"CORS": "*", "CORSCredentials": false}}