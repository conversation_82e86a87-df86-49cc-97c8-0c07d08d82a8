#!/bin/bash

# Dependency Optimization Script for Esus Audit AI
# This script cleans up duplicate dependencies and reinstalls with workspace optimization

set -e

echo "🧹 Starting dependency optimization..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "client" ] || [ ! -d "server" ]; then
    print_error "Please run this script from the esus-audit-ai root directory"
    exit 1
fi

print_status "Cleaning existing node_modules..."

# Remove all node_modules directories
rm -rf node_modules
rm -rf client/node_modules
rm -rf server/node_modules
rm -rf shared/node_modules

# Remove package-lock files
rm -f package-lock.json
rm -f client/package-lock.json
rm -f server/package-lock.json
rm -f shared/package-lock.json

print_success "Cleaned existing dependencies"

print_status "Installing optimized dependencies..."

# Install dependencies with workspace support
npm install

print_success "Dependencies installed with workspace optimization"

print_status "Verifying workspace setup..."

# Check if workspaces are properly configured
if npm ls --workspaces > /dev/null 2>&1; then
    print_success "Workspace configuration verified"
else
    print_warning "Workspace configuration may have issues"
fi

print_status "Running dependency audit..."

# Check for vulnerabilities
npm audit --audit-level=moderate || print_warning "Some vulnerabilities found - consider running 'npm audit fix'"

print_status "Checking for duplicate dependencies..."

# List duplicate dependencies
npm ls --all 2>/dev/null | grep -E "deduped|UNMET" || print_success "No obvious duplicates found"

print_success "Dependency optimization complete!"

echo ""
echo "📊 Summary:"
echo "- Converted to npm workspaces"
echo "- Moved shared dependencies to root"
echo "- Created shared utilities workspace"
echo "- Eliminated duplicate axios and jspdf"
echo "- Optimized testing library usage"

echo ""
echo "🚀 Next steps:"
echo "1. Run 'npm test' to verify all tests pass"
echo "2. Run 'npm run build' to verify builds work"
echo "3. Check the docs/DEPENDENCY_OPTIMIZATION.md for details"

echo ""
print_success "All done! Your dependencies are now optimized."
